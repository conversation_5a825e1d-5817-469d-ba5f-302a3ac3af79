# listOpenedFiles 协议文档

## 概述

`listOpenedFiles` 协议用于获取当前在 IDE 中已打开的文件列表。该协议返回文件的相对路径列表，**按文件访问顺序排序**（最近访问的文件在前）。

## 协议详情

### 请求

- **协议名称**: `listOpenedFiles`
- **请求参数**: 无需参数
- **请求体**: 空对象 `{}`

### 响应

- **响应类型**: `List<String>`
- **响应内容**: 包含已打开文件相对路径的字符串数组，按文件访问顺序排序

## 排序策略

该协议使用多层策略来确保返回正确的文件访问顺序：

### 策略1: EditorComposite 激活时间（最优）
- 使用 `EditorComposite` 的内部激活时间戳来确定文件的访问顺序
- 通过反射获取每个标签页的最后激活时间
- 最准确反映用户的实际文件访问行为，无需额外监听器

### 策略2: EditorHistoryManager
- 使用 IntelliJ Platform 的 `EditorHistoryManager` 获取文件访问历史
- 按最近访问顺序排序（最近访问的在前）
- 只返回当前仍然打开的文件

### 策略3: 当前选中文件优先
- 将当前选中的文件放在列表最前面
- 其他已打开文件按某种顺序排列

### 策略4: EditorWindow.fileList
- 使用当前编辑器窗口的文件列表
- 反映标签页的实际顺序

### 策略5: FileEditorManager.openFiles（兜底）
- 如果前面的策略都失败，使用基本的已打开文件列表
- 顺序可能不够准确，但确保能返回结果

## 示例

### JavaScript 调用示例

```javascript
// 发送请求获取已打开的文件列表
const message = {
    id: "unique-request-id",
    type: "listOpenedFiles",
    body: {},
    needResponse: true
};

// 发送消息到 IDE
window.postMessage({
    type: "tocoIdeaPlugin-request",
    message: message
}, "*");

// 监听响应
window.addEventListener("message", (event) => {
    if (event.data.type === "tocoIdeaPlugin-response") {
        const response = event.data.message;
        if (response.id === "unique-request-id" && response.type === "listOpenedFiles") {
            const openedFiles = response.body;
            console.log("已打开的文件（按最近访问顺序）:", openedFiles);
            // 示例输出: ["src/main/java/Main.java", "src/test/java/TestMain.java", "README.md"]
            // 第一个文件是最近访问的
        }
    }
});
```

### 响应示例

```json
{
    "id": "unique-request-id",
    "type": "listOpenedFiles",
    "code": 200,
    "body": [
        "src/main/java/com/example/service/UserService.java",
        "src/main/java/com/example/Main.java",
        "src/test/java/com/example/MainTest.java",
        "README.md",
        "pom.xml"
    ]
}
```

## 实现细节

### 访问记录机制

1. **EditorComposite 时间戳**: 通过反射获取每个标签页的最后激活时间
2. **多窗口支持**: 遍历所有编辑器窗口，收集所有标签页的激活时间
3. **时间排序**: 按激活时间降序排序，最近激活的文件在前
4. **无需监听器**: 直接读取现有的时间戳，不需要额外的事件监听

### 路径处理

1. **相对路径计算**: 协议会自动将文件的绝对路径转换为相对于项目根目录的相对路径
2. **路径分隔符处理**: 自动移除路径开头的分隔符（`/` 或 `\`）
3. **异常处理**: 如果无法计算相对路径，则返回文件名

### 错误处理

- 如果项目为 null，返回空数组
- 如果获取文件列表时发生异常，记录警告日志并返回空数组
- 所有异常都会被捕获，确保协议调用不会导致崩溃
- 多层策略确保即使某个策略失败，仍能返回结果

## 使用场景

1. **最近文件列表**: 在 WebView 中显示按访问顺序排列的文件列表
2. **快速切换**: 提供基于访问历史的文件快速切换功能
3. **工作流优化**: 根据用户的文件访问模式提供智能建议
4. **状态同步**: 保持 WebView 与 IDE 的文件状态和访问历史同步

## 注意事项

1. **顺序保证**: 使用多层策略确保返回的文件列表有合理的顺序
2. **性能考虑**: 优先使用高效的 API，避免性能问题
3. **兼容性**: 兼容不同版本的 IntelliJ Platform
4. **实时性**: 反映当前的文件打开状态和访问历史

## 技术实现

### 核心方法

```kotlin
// 获取 EditorComposite 的激活时间
private fun getCompositeActivationTime(composite: EditorComposite): Long {
    return try {
        // 尝试通过反射获取激活时间字段
        val field = composite.javaClass.getDeclaredField("lastActivationTime")
        field.isAccessible = true
        val time = field.get(composite) as? Long
        if (time != null && time > 0) time else 0L
    } catch (e: Exception) {
        0L // 如果获取失败，返回0
    }
}

// 获取按访问顺序排列的文件列表
private fun getOpenedFilesInRecentOrder(): List<String> {
    // 策略1: EditorComposite 激活时间
    // 策略2: EditorHistoryManager
    // 策略3: 当前选中文件优先
    // 策略4: EditorWindow.fileList
    // 策略5: FileEditorManager.openFiles
}
```

### 关键特性

- **EditorComposite 时间戳**: 直接读取标签页的激活时间，最准确的访问顺序
- **多层回退**: 确保在任何情况下都能返回结果
- **反射安全**: 使用反射时有完善的异常处理
- **无需监听器**: 不需要额外的事件监听，减少系统负担
